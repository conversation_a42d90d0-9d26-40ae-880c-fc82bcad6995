import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/utils/tracking_permission_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/data/model/coturn_config_bo_model.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_base.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_model.dart';
import 'package:flutter_audio_room/services/punishment_service/punishment_service.dart';
import 'package:flutter_audio_room/services/websocket_service/core/constants/websocket_url.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as webrtc;

/// 处理好友通话的 WebSocket 通信相关的逻辑
mixin FriendWebSocketMixin on VoiceCallBaseMixin {
  @override
  WebSocketUrl get webSocketUrl => FriendVoiceCallWebSocketUrl();

  @protected
  WebSocketDataSource? webSocketDatasource;
  StreamSubscription<WebsocketResponse>? messageStreamSubscription;

  Timer? _callTimer;

  Future<void> initializeWebSocket() async {
    final result =
        await getIt<WebSocketDataSourceManager>().connect(webSocketUrl.url);
    webSocketDatasource = result.getRight();
    messageStreamSubscription = webSocketDatasource?.messageStream
        .listen(_handleWebSocketMessage, onError: (error) {
      LogUtils.e('friend WebSocket error: $error', tag: 'WebSocket');
    });
  }

  Future<void> disposeWebSocket() async {
    messageStreamSubscription?.cancel();
    getIt<WebSocketDataSourceManager>().disposeTarget(webSocketUrl.url);
  }

  /// 清理通话定时器
  void disposeCallTimer() {
    _callTimer?.cancel();
    _callTimer = null;
  }

  Future<void> sendWebSocketMessage({
    required String path,
    required Map<String, dynamic> data,
    Function()? onSuccess,
  }) async {
    // 检查WebSocket连接是否可用
    if (webSocketDatasource == null || !webSocketDatasource!.isConnected) {
      LogUtils.w('WebSocket is not connected, skipping message send',
          tag: 'FriendWebSocketMixin.sendWebSocketMessage');
      return;
    }

    final message = {'path': '${webSocketUrl.paramsPath}$path', ...data};
    final result = await webSocketDatasource?.sendMessage(
      WebSocketMessageEntity(
        data: jsonEncode(message),
        type: WebSocketMessageType.json,
      ),
    );

    result?.fold(
      (error) async {
        handleError(error.message);
      },
      (response) {
        onSuccess?.call();
      },
    );
  }

  void _handleWebSocketMessage(WebsocketResponse response) async {
    final data = response.data.extraData;
    final code = response.code ?? '';
    final message = response.msg ?? '';
    if (getIt<PunishmentService>().isPunishmentCode(code)) {
      final punishment = response.data.punishment ?? const PunishmentModel();
      state = state.copyWith(
        callStatus: CallDisconnected(CallDisconnectReason.manualLeave),
        matchStatus: MatchRestricted(
          message: message,
          code: code,
          expireTime: punishment.expireTime,
        ),
      );
      this.punishment = PunishmentException(
        punishment: punishment,
        identifier: code,
        message: message,
      );
      return;
    }

    if (response.success == false) {
      LogUtils.e('WebSocket error: $message', tag: 'WebSocket');
      if (code != 'active.request.exist') {
        LoadingUtils.showToast(message);
        return;
      }
    }

    final shouldClose = response.shouldClose;

    if (shouldClose == true) {
      state = state.copyWith(
        callStatus: CallDisconnected(CallDisconnectReason.error),
        matchStatus: MatchError(message),
      );
      return;
    }

    final handlers = {
      '/voiceCall/request': _handleCall,
      '/voiceCall/answerCall': _handleAnswerCall,
      '/voiceCall/offer': handleOfferMessage,
      '/voiceCall/answer': handleAnswerMessage,
      '/voiceCall/candidate': _handleCandidate,
      '/voiceCall/end': (_) async {
        disposeWebRTC();
        disposeTimer();
        // sendEndCallMessage();
        state = state.copyWith(
          callStatus: CallDisconnected(CallDisconnectReason.peerLeave),
        );
      }
    };

    final handler = handlers[response.path];
    if (handler != null) {
      callbackQueue?.addCallback(
        () async {
          try {
            await handler(data);
          } catch (e) {
            LogUtils.e('Friend WebSocket error: ${handler.toString()}',
                tag: 'WebSocket', error: e);
          }
        },
      );
    }
  }

  // 发起通话
  Future<void> startCall(String calleeId) async {
    await sendWebSocketMessage(
      path: '/request',
      data: {'calleeId': calleeId},
      onSuccess: () {
        state = state.copyWith(
          callStatus: WaitingForAnswer(),
          isCaller: true,
          duration: const Duration(seconds: 0),
          peerId: calleeId,
        );

        // Track friend voice call started
        TrackingPermissionUtils.trackFriendVoiceCall(
          eventType: 'call_started',
          callId: state.callId,
          friendId: calleeId,
          isCaller: true,
          success: true,
        );

        final timeoutSeconds = ref
            .read(coreServiceProvider)
            .config
            .rtcConfigResp
            .voiceCallConfigResp
            .pendingTimeoutSeconds;

        _callTimer?.cancel();
        _callTimer = Timer(Duration(seconds: timeoutSeconds), () async {
          if (state.callStatus is CallConnected) {
            _callTimer?.cancel();
            return;
          }
          state = state.copyWith(
            callStatus: CallError('Call timeout'),
          );
          endCall();

          final peerId = state.peerId;

          if (peerId != null) {
            final userId = await ref.read(userIdProvider.future);

            ref.read(chatProvider(peerId).notifier).sendMessage(
                  VoiceCallEndMessageConfig(
                    userId: userId,
                    callId: state.isCaller ? userId : peerId,
                    callerId: state.isCaller ? userId : peerId,
                    calleeId: state.isCaller ? peerId : userId,
                    duration: 0,
                    callEndType: CallEndType.timeout,
                  ),
                );
          }
        });
      },
    );
  }

  // 接受通话
  Future<void> acceptCall() async {
    final publicKey = cryptoService.exportPublicKey();
    if (publicKey.isLeft()) {
      throw Exception('Failed to export public key');
    }

    await sendWebSocketMessage(
      path: '/answerCall',
      data: {
        'requestId': state.callId,
        'answerType': 'accept',
        'publicKey': publicKey.getRight(),
      },
      onSuccess: () {
        state = state.copyWith(
          callStatus: CallConnecting(),
        );

        // Track friend voice call answered
        TrackingPermissionUtils.trackFriendVoiceCall(
          eventType: 'call_answered',
          callId: state.callId,
          friendId: state.peerId,
          friendName: state.peerNickName,
          isCaller: false,
          success: true,
        );
      },
    );
  }

  // 拒绝通话
  Future<void> rejectCall() async {
    await sendWebSocketMessage(
      path: '/answerCall',
      data: {'requestId': state.callId, 'answerType': 'reject'},
      onSuccess: () {
        state = state.copyWith(
          callStatus: CallDisconnected(CallDisconnectReason.manualLeave),
        );

        // Track friend voice call rejected
        TrackingPermissionUtils.trackFriendVoiceCall(
          eventType: 'call_rejected',
          callId: state.callId,
          friendId: state.peerId,
          friendName: state.peerNickName,
          isCaller: false,
          success: true,
        );
      },
    );
    final peerId = state.peerId;

    if (peerId != null) {
      final userId = await ref.read(userIdProvider.future);

      ref.read(chatProvider(peerId).notifier).sendMessage(
            VoiceCallEndMessageConfig(
              userId: userId,
              callId: state.isCaller ? userId : peerId,
              callerId: state.isCaller ? userId : peerId,
              calleeId: state.isCaller ? peerId : userId,
              duration: 0,
              callEndType: CallEndType.reject,
            ),
          );
    }
    endCall(answerCall: true);
  }

  @override
  Future<void> sendIceCandidate(webrtc.RTCIceCandidate candidate) async {
    await sendWebSocketMessage(
      path: '/candidate',
      data: {
        'requestId': state.callId,
        'ice': {
          'sdp': candidate.candidate,
          'sdpMid': candidate.sdpMid,
          'sdpMLineIndex': candidate.sdpMLineIndex,
        }
      },
    );
  }

  @override
  Future<void> sendOfferMessage(webrtc.RTCSessionDescription sdp) async {
    await sendWebSocketMessage(
      path: '/offer',
      data: {
        'requestId': state.callId,
        'sdp': {
          'type': sdp.type,
          'desc': sdp.sdp,
        },
        'sessionKey': encryptedSessionKey,
      },
    );
  }

  @override
  Future<void> sendAnswerMessage(webrtc.RTCSessionDescription sdp) async {
    await sendWebSocketMessage(
      path: '/answer',
      data: {
        'requestId': state.callId,
        'sdp': {
          'type': sdp.type,
          'desc': sdp.sdp,
        },
      },
    );
  }

  @override
  Future<void> sendConnectedMessage(String connectType, String protocol) async {
    await sendWebSocketMessage(
      path: '/connected',
      data: {
        "requestId": state.callId,
      },
    );
  }

  @override
  Future<void> sendEndCallMessage() async {
    if (state.callId == null) return;

    if (state.callStatus is CallDisconnected) {
      final reason = (state.callStatus as CallDisconnected).reason;
      await sendWebSocketMessage(
        path: '/end',
        data: {'requestId': state.callId, 'endReason': reason.name},
      );

      // Track friend voice call ended
      String callEndReason;
      switch (reason) {
        case CallDisconnectReason.manualLeave:
        case CallDisconnectReason.hangup:
          callEndReason = 'user_hangup';
        case CallDisconnectReason.peerLeave:
          callEndReason = 'peer_hangup';
        case CallDisconnectReason.error:
          callEndReason = 'network_error';
        case CallDisconnectReason.cancel:
          callEndReason = 'cancelled';
      }

      TrackingPermissionUtils.trackFriendVoiceCall(
        eventType: 'call_ended',
        callId: state.callId,
        friendId: state.peerId,
        friendName: state.peerNickName,
        callDuration: state.duration,
        callEndReason: callEndReason,
        isEncrypted: state.isAudioEncryptEnabled,
        isCaller: state.isCaller,
        success: true,
      );

      final peerId = state.peerId;

      if (peerId != null) {
        final userId = await ref.read(userIdProvider.future);

        ref.read(chatProvider(peerId).notifier).sendMessage(
              VoiceCallEndMessageConfig(
                userId: userId,
                callId: state.isCaller ? userId : peerId,
                callerId: state.isCaller ? userId : peerId,
                calleeId: state.isCaller ? peerId : userId,
                duration: state.duration.inSeconds,
                callEndType: state.duration.inSeconds > 0
                    ? CallEndType.success
                    : CallEndType.timeout,
              ),
            );
      }
    }
  }

  @override
  Future<void> endCall({
    bool answerCall = false,
  }) async {
    try {
      if (state.callStatus is WaitingForAnswer) {
        state = state.copyWith(
          callStatus: CallDisconnected(CallDisconnectReason.cancel),
        );
      } else {
        // 更新状态
        state = state.copyWith(
          callStatus: CallDisconnected(CallDisconnectReason.hangup),
        );
      }
      if (!answerCall) {
        sendEndCallMessage();
      }
      // 只清理 WebRTC 相关资源
      disposeWebRTC();
      disposeTimer();
    } catch (e) {
      LogUtils.e('Error ending call', tag: 'AudioStream', error: e);
    }
  }

  // 处理来电
  Future<void> _handleCall(Map<String, dynamic> message) async {
    final callId = message['requestId'] as String?;
    final peerId = message['userId'] as String?;
    final peerNickName = message['nickName'] as String?;
    final Map<String, dynamic> peerAvatar = message['avatar'] ?? {};
    if (callId == null) return;

    if (peerId == null) {
      state = state.copyWith(
        callId: callId,
        isCaller: true,
        peerNickName: peerNickName,
        peerAvatar: peerAvatar,
        callStatus: WaitingForAnswer(),
      );
    } else {
      state = state.copyWith(
        callId: callId,
        peerId: peerId,
        peerNickName: peerNickName,
        peerAvatar: peerAvatar,
        isCaller: false,
        callStatus: CallIncoming(),
      );
    }
  }

  Future<void> _handleAnswerCall(Map<String, dynamic> message) async {
    _callTimer?.cancel();
    if (message['answerType'] == 'reject') {
      _handleReject(message);
    } else {
      _handleAccept(message);
    }
  }

  // 处理对方接受通话
  Future<void> _handleAccept(Map<String, dynamic> message) async {
    final coturnConfigBO = CoturnConfigBOModel.fromJson(
      message['coturnConfigBO'] ?? {},
    );

    await createPeerConnection(coturnConfigBO);
    if (state.isCaller) {
      final peerPublicKey = message['publicKey'] as String?;
      if (peerPublicKey == null) {
        throw Exception('Peer public key is null');
      }
      await generateSessionKey(peerPublicKey);
      await createOffer();
    }
  }

  // 处理对方拒绝通话
  Future<void> _handleReject(Map<String, dynamic> message) async {
    state = state.copyWith(
      callStatus: CallDisconnected(CallDisconnectReason.manualLeave),
    );
  }

  Future<void> _handleCandidate(Map<String, dynamic> message) async {
    if (state.peerConnection == null) return;

    final candidate = webrtc.RTCIceCandidate(
      message['sdp'],
      message['sdpMid'],
      message['sdpMLineIndex'],
    );

    if (!hasSetRemoteDescription) {
      iceCandidatesToAdd.add(candidate);
    } else {
      await webRtcService.addIceCandidate(state.peerConnection!, candidate);
    }
  }
}
