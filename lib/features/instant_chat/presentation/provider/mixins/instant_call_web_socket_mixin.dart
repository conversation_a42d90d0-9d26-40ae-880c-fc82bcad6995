import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/utils/tracking_permission_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/chat_provider.dart';
import 'package:flutter_audio_room/features/instant_chat/data/model/coturn_config_bo_model.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/call_status.dart';
import 'package:flutter_audio_room/features/instant_chat/domain/entities/match_status.dart';
import 'package:flutter_audio_room/features/voice_call/base/provider/voice_call_base.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_exception.dart';
import 'package:flutter_audio_room/services/punishment_service/model/punishment_model.dart';
import 'package:flutter_audio_room/services/punishment_service/punishment_service.dart';
import 'package:flutter_audio_room/services/websocket_service/core/constants/websocket_url.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource.dart';
import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as webrtc;

/// 处理 WebSocket 通信相关的逻辑
mixin InstantCallWebSocketMixin on VoiceCallBaseMixin {
  @override
  WebSocketUrl get webSocketUrl => InstantVoiceCallWebSocketUrl();

  @protected
  WebSocketDataSource? webSocketDatasource;
  StreamSubscription<WebsocketResponse>? messageStreamSubscription;

  Timer? _matchTimer;

  Future<void> initializeWebSocket() async {
    state = state.copyWith(matchStatus: MatchInProgress());
    final result =
        await getIt<WebSocketDataSourceManager>().connect(webSocketUrl.url);
    webSocketDatasource = result.getRight();
    messageStreamSubscription =
        webSocketDatasource?.messageStream.listen(_handleWebSocketMessage);
  }

  Future<void> disposeWebSocket() async {
    await messageStreamSubscription?.cancel();
    getIt<WebSocketDataSourceManager>().disposeTarget(webSocketUrl.url);
  }

  /// 清理匹配定时器
  void disposeMatchTimer() {
    _matchTimer?.cancel();
    _matchTimer = null;
  }

  Future<void> sendWebSocketMessage({
    required String path,
    required Map<String, dynamic> data,
    Function()? onSuccess,
  }) async {
    // 检查WebSocket连接是否可用
    if (webSocketDatasource == null || !webSocketDatasource!.isConnected) {
      LogUtils.w('WebSocket is not connected, skipping message send',
          tag: 'InstantCallWebSocketMixin.sendWebSocketMessage');
      return;
    }

    final message = {'path': '${webSocketUrl.paramsPath}$path', ...data};
    final result = await webSocketDatasource?.sendMessage(
      WebSocketMessageEntity(
        data: jsonEncode(message),
        type: WebSocketMessageType.json,
      ),
    );

    result?.fold(
      (error) => handleError(error.message),
      (response) {
        onSuccess?.call();
      },
    );
  }

  void _handleWebSocketMessage(WebsocketResponse response) async {
    _matchTimer?.cancel();

    final data = response.data.extraData;
    final code = response.code ?? '';
    final message = response.msg ?? '';
    if (getIt<PunishmentService>().isPunishmentCode(code)) {
      final punishment = response.data.punishment ?? const PunishmentModel();
      state = state.copyWith(
        callStatus: CallDisconnected(CallDisconnectReason.manualLeave),
        matchStatus: MatchRestricted(
          message: message,
          code: code,
          expireTime: punishment.expireTime,
        ),
      );
      this.punishment = PunishmentException(
        punishment: punishment,
        identifier: code,
        message: message,
      );
      return;
    }

    if (response.success == false) {
      LogUtils.e('WebSocket error: $message', tag: 'WebSocket');
      if (code != 'match.active.request.exist') {
        LoadingUtils.showToast(message);
        return;
      }
    }

    final shouldClose = response.shouldClose;

    if (shouldClose == true) {
      state = state.copyWith(
        callStatus: CallDisconnected(CallDisconnectReason.error),
        matchStatus: MatchError(message),
      );

      // Track instant voice call failed
      TrackingPermissionUtils.trackInstantVoiceCall(
        eventType: 'call_failed',
        matchId: state.callId,
        peerId: state.peerId,
        callDuration: state.duration,
        callEndReason: 'network_error',
        success: false,
        errorMessage: message,
      );

      return;
    }

    final handlers = {
      '/instantVoiceCall/startMatch': handleMatch,
      '/instantVoiceCall/offer': handleOfferMessage,
      '/instantVoiceCall/answer': handleAnswerMessage,
      '/instantVoiceCall/candidate': handleCandidate,
      '/instantVoiceCall/end': (_) async {
        state = state.copyWith(
          callStatus: CallDisconnected(CallDisconnectReason.peerLeave),
        );
      }
    };

    final handler = handlers[response.path];
    if (handler != null) {
      callbackQueue?.addCallback(
        () async {
          try {
            await handler(data);
          } catch (e) {
            LogUtils.e('WebSocket error: ${handler.toString()}',
                tag: 'WebSocket', error: e);
          }
        },
      );
    }
  }

  Future<void> startMatch() async {
    final publicKey = cryptoService.exportPublicKey();
    if (publicKey.isLeft()) {
      state = state.copyWith(
        matchStatus: MatchError('Failed to match, please try again'),
      );
      return;
    }

    await sendWebSocketMessage(
      path: '/startMatch',
      data: {'publicKey': publicKey.getRight()},
      onSuccess: () {
        if (state.matchStatus is MatchInProgress) return;
        state = state.copyWith(
          matchStatus: MatchInProgress(),
          duration: const Duration(seconds: 0),
        );

        // Track instant voice call started
        TrackingPermissionUtils.trackInstantVoiceCall(
          eventType: 'call_started',
          matchId: state.callId,
          success: true,
        );
      },
    );

    final timeoutSeconds = ref
        .read(coreServiceProvider)
        .config
        .rtcConfigResp
        .instantVoiceCallConfigResp
        .pendingTimeoutSeconds;

    _matchTimer?.cancel();
    _matchTimer = Timer(Duration(seconds: timeoutSeconds), () async {
      if (state.matchStatus is MatchSuccess) {
        _matchTimer?.cancel();
        return;
      }

      // 检查WebSocket是否仍然连接，避免在dispose后发送消息
      if (webSocketDatasource == null || !webSocketDatasource!.isConnected) {
        LogUtils.w('WebSocket disconnected, skipping timeout cancel message',
            tag: 'InstantCallWebSocketMixin._matchTimer');
        return;
      }

      state = state.copyWith(
        matchStatus: MatchError('No one answered, please try again later'),
      );
      await sendWebSocketMessage(
        path: '/cancel',
        data: {
          'matchId': state.callId,
          'isTimeout': true,
        },
      );
    });
  }

  Future<void> cancelMatch() async {
    if (state.matchStatus is MatchSuccess) return;
    state = state.copyWith(matchStatus: const MatchNotStarted());
    await sendWebSocketMessage(
      path: '/cancel',
      data: {
        'matchId': state.callId,
        'isTimeout': false,
      },
    );
    await disposeWebSocket();
    disposeTimer();
    disposeMatchTimer(); // 使用新的方法清理匹配定时器
  }

  Future<void> handleMatch(Map<String, dynamic> message) async {
    final matchId = message['matchId'] as String?;
    final peerId = message['userId'] as String?;
    final peerNickName = message['nickName'] as String?;
    final Map<String, dynamic> peerAvatar = message['avatar'] ?? {};
    final peerPublicKey = message['publicKey'] as String?;
    final coturnConfigBO = CoturnConfigBOModel.fromJson(
      message['coturnConfigBO'] ?? {},
    );
    if (matchId == null) return;

    final isCaller = message['isCaller'] ?? false;

    state = state.copyWith(
      matchStatus: MatchSuccess(matchId),
      callId: matchId,
      callStatus: CallConnecting(),
      isCaller: isCaller,
      peerId: peerId,
      peerNickName: peerNickName,
      peerAvatar: peerAvatar,
    );

    // Track match found and call connecting
    TrackingPermissionUtils.trackInstantVoiceCall(
      eventType: 'match_found',
      matchId: matchId,
      peerId: peerId,
      success: true,
      additionalParams: {
        'is_caller': isCaller,
        'peer_nickname': peerNickName ?? '',
      },
    );

    if (peerPublicKey == null) {
      throw Exception('Peer public key is null');
    }

    await createPeerConnection(coturnConfigBO);

    if (isCaller) {
      LogUtils.d('Caller: Will send public key to peer', tag: 'WebSocket');
      await generateSessionKey(peerPublicKey);
      await createOffer();
    } else {
      LogUtils.d('Callee: Waiting for caller\'s public key', tag: 'WebSocket');
    }
  }

  @override
  Future<void> sendOfferMessage(webrtc.RTCSessionDescription sdp) async {
    await sendWebSocketMessage(
      path: '/offer',
      data: {
        'matchId': state.callId,
        'sessionKey': encryptedSessionKey,
        'sdp': {
          'type': sdp.type,
          'desc': sdp.sdp,
        },
      },
    );
  }

  @override
  Future<void> sendAnswerMessage(
    webrtc.RTCSessionDescription sdp,
  ) async {
    await sendWebSocketMessage(
      path: '/answer',
      data: {
        'matchId': state.callId,
        'sdp': {
          'type': sdp.type,
          'desc': sdp.sdp,
        },
      },
    );
  }

  Future<void> handleCandidate(Map<String, dynamic> message) async {
    if (state.peerConnection == null) return;

    final candidate = webrtc.RTCIceCandidate(
      message['sdp'],
      message['sdpMid'],
      message['sdpMLineIndex'],
    );

    if (!hasSetRemoteDescription) {
      iceCandidatesToAdd.add(candidate);
    } else {
      await webRtcService.addIceCandidate(state.peerConnection!, candidate);
    }
  }

  @override
  Future<void> sendIceCandidate(webrtc.RTCIceCandidate candidate) async {
    await sendWebSocketMessage(
      path: '/candidate',
      data: {
        'matchId': state.callId,
        'ice': {
          'sdp': candidate.candidate,
          'sdpMid': candidate.sdpMid,
          'sdpMLineIndex': candidate.sdpMLineIndex,
        },
      },
    );
  }

  @override
  Future<void> sendConnectedMessage(String connectType, String protocol) async {
    await sendWebSocketMessage(
      path: '/connected',
      data: {
        "matchId": state.callId,
        "connectType": connectType,
        "protocol": protocol,
      },
    );
  }

  @override
  Future<void> sendEndCallMessage() async {
    if (state.callId == null) return;

    if (state.callStatus is CallDisconnected) {
      final reason = (state.callStatus as CallDisconnected).reason;
      await sendWebSocketMessage(path: "/end", data: {
        "matchId": state.callId,
        "endReason": reason.name,
        "duration": state.duration.inSeconds,
      });

      // Track instant voice call ended
      String callEndReason;
      switch (reason) {
        case CallDisconnectReason.manualLeave:
          callEndReason = 'user_hangup';
          break;
        case CallDisconnectReason.peerLeave:
          callEndReason = 'peer_hangup';
          break;
        case CallDisconnectReason.error:
          callEndReason = 'network_error';
          break;
        default:
          callEndReason = 'unknown';
      }

      TrackingPermissionUtils.trackInstantVoiceCall(
        eventType: 'call_ended',
        matchId: state.callId,
        peerId: state.peerId,
        callDuration: state.duration,
        callEndReason: callEndReason,
        isEncrypted: state.isAudioEncryptEnabled,
        success: true,
      );

      final peerId = state.peerId;
      if (peerId != null) {
        final userId = await ref.read(userIdProvider.future);

        ref.read(chatProvider(peerId).notifier).sendMessage(
              VoiceCallEndMessageConfig(
                userId: userId,
                callId: state.isCaller ? userId : peerId,
                callerId: state.isCaller ? userId : peerId,
                calleeId: state.isCaller ? peerId : userId,
                duration: state.duration.inSeconds,
                callEndType: CallEndType.success,
              ),
            );
      }
    }
  }
}
